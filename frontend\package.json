{"name": "realestate-contract-platform-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "clean": "rm -rf .next out dist", "analyze": "cross-env ANALYZE=true next build", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "prepare": "cd .. && husky install frontend/.husky"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-form": "^0.1.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.1.5", "@tailwindcss/forms": "0.5.9", "@tailwindcss/typography": "0.5.15", "@tanstack/react-query": "5.62.7", "@types/node": "22.10.2", "@types/react": "19.0.2", "@types/react-dom": "19.0.2", "autoprefixer": "10.4.20", "axios": "1.7.9", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "date-fns": "4.1.0", "framer-motion": "11.15.0", "lucide-react": "0.468.0", "next": "15.1.3", "postcss": "^8.4.31", "react": "19.0.0", "react-dom": "19.0.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-pdf": "9.1.1", "recharts": "^2.14.1", "tailwind-merge": "2.5.5", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "5.7.2", "zod": "^3.24.1", "zustand": "5.0.2"}, "devDependencies": {"@playwright/test": "^1.54.2", "@storybook/addon-essentials": "8.4.7", "@storybook/addon-interactions": "8.4.7", "@storybook/addon-links": "8.4.7", "@storybook/blocks": "8.4.7", "@storybook/nextjs": "8.4.7", "@storybook/react": "8.4.7", "@storybook/test": "8.4.7", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.1.0", "@testing-library/user-event": "14.5.2", "@types/jest": "29.5.14", "@typescript-eslint/eslint-plugin": "8.18.2", "@typescript-eslint/parser": "8.18.2", "cross-env": "7.0.3", "eslint": "9.17.0", "eslint-config-next": "15.1.3", "husky": "9.1.7", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "lint-staged": "15.2.11", "prettier": "3.4.2", "prettier-plugin-tailwindcss": "0.6.9", "storybook": "8.4.7"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}